{"$schema": "https://turbo.build/schema.json", "ui": "tui", "remoteCache": {"signature": true}, "tasks": {"build": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", ".env*"], "outputs": [".next/**", "!.next/cache/**", "dist/**"], "cache": true}, "dev": {"cache": false, "persistent": true}, "db:generate": {"cache": true, "inputs": ["prisma/schema.prisma"], "outputs": ["node_modules/.prisma/**"]}}}