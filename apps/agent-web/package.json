{"name": "agent-web", "version": "0.0.1", "type": "module", "private": true, "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint src --ext ts,tsx --fix", "typecheck": "tsc --noEmit"}, "dependencies": {"@radix-ui/react-slot": "^1.1.2", "@tanstack/react-query": "^5.81.5", "@tanstack/react-query-devtools": "^5.81.5", "@workspace/ui": "workspace:*", "react": "^19.0.0", "react-dom": "^19.0.0", "react-router-dom": "^7.1.0"}, "devDependencies": {"@tailwindcss/postcss": "^4.0.8", "@types/react": "^19", "@types/react-dom": "^19", "@vitejs/plugin-react": "^4.3.4", "@workspace/eslint-config": "workspace:*", "@workspace/typescript-config": "workspace:*", "autoprefixer": "^10.4.20", "postcss": "^8.5.0", "tailwindcss": "^4.0.8", "typescript": "^5.7.3", "vite": "^6.0.0"}}