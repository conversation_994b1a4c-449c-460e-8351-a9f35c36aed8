{"name": "hono-api", "version": "0.0.1", "private": true, "type": "module", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsup", "start": "node dist/index.js", "lint": "eslint src --ext ts --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint src --ext ts --fix", "typecheck": "tsc --noEmit"}, "dependencies": {"hono": "^4.6.0", "@hono/node-server": "^1.14.0", "zod": "^3.23.8"}, "devDependencies": {"@types/node": "^20", "@workspace/eslint-config": "workspace:*", "@workspace/typescript-config": "workspace:*", "tsup": "^8.3.5", "tsx": "^4.19.2", "typescript": "^5.7.3"}}