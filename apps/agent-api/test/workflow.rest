### Workflow API REST Client 测试文件
### 使用 VS Code REST Client 扩展运行这些测试

@baseUrl = http://localhost:3001/api
@contentType = application/json

### 1. 健康检查
GET {{baseUrl}}/health
Content-Type: {{contentType}}

### 2. 生成工作流 DSL - 简单示例
# @name generateSimpleDsl
POST {{baseUrl}}/workflows/generate-dsl
Content-Type: {{contentType}}

{
  "userMessage": "创建一个简单的工作流：用户输入一个问候语，工作流调用智能体，智能体可以自动调用获取时间的工具，然后将结果返回给用户。"
}

### 3. 生成工作流 DSL - 复杂示例
# @name generateComplexDsl
POST {{baseUrl}}/workflows/generate-dsl
Content-Type: {{contentType}}

{
  "userMessage": "创建一个客户服务工作流：1. 接收客户问题 2. 分析问题类型 3. 如果是技术问题，转给技术支持智能体处理 4. 如果是销售问题，转给销售智能体处理 5. 生成回复"
}

### 4. 使用生成的 DSL 创建工作流实例
# @name createWorkflowFromDsl
POST {{baseUrl}}/workflows
Content-Type: {{contentType}}

{
  "name": "邮件摘要工作流",
  "description": "自动摘要邮件内容的工作流",
  "dsl": {
    "id": "emailSummaryWorkflow",
    "name": "邮件摘要工作流",
    "description": "自动摘要邮件内容",
    "version": "v1",
    "tools": ["getCurrentTime"],
    "agents": [
      {
        "name": "emailSummarizerAgent",
        "description": "邮件摘要智能体",
        "prompt": "你是一个专业的邮件摘要助手，请将输入的邮件内容进行简洁的摘要。",
        "output": {
          "summary": "string",
          "keyPoints": "array"
        },
        "tools": []
      }
    ],
    "content": {},
    "events": [
      {
        "type": "WORKFLOW_START",
        "data": {
          "emailContent": "string"
        }
      },
      {
        "type": "SUMMARY_GENERATED",
        "data": {
          "summary": "string",
          "keyPoints": "array"
        }
      },
      {
        "type": "WORKFLOW_STOP",
        "data": {
          "result": "object"
        }
      }
    ],
    "steps": [
      {
        "event": "WORKFLOW_START",
        "handle": "async (event, context) => { const result = await emailSummarizerAgent.run(event.data.emailContent); return { type: 'SUMMARY_GENERATED', data: result }; }"
      },
      {
        "event": "SUMMARY_GENERATED",
        "handle": "async (event, context) => { const timestamp = await getCurrentTime(); return { type: 'WORKFLOW_STOP', data: { result: { ...event.data, timestamp } } }; }"
      }
    ]
  }
}

### 5. 手动创建简单工作流
# @name createSimpleWorkflow
POST {{baseUrl}}/workflows
Content-Type: {{contentType}}

{
  "name": "时间查询工作流",
  "description": "简单的时间查询工作流",
  "dsl": {
    "id": "timeQueryWorkflow",
    "name": "时间查询工作流",
    "description": "查询当前时间",
    "version": "v1",
    "tools": ["getCurrentTime"],
    "content": {},
    "events": [
      {
        "type": "WORKFLOW_START",
        "data": {
          "timezone": "string"
        }
      },
      {
        "type": "WORKFLOW_STOP",
        "data": {
          "currentTime": "string"
        }
      }
    ],
    "steps": [
      {
        "event": "WORKFLOW_START",
        "handle": "async (event, context) => { const time = await getCurrentTime({ timezone: event.data.timezone }); return { type: 'WORKFLOW_STOP', data: { currentTime: time } }; }"
      }
    ]
  }
}

### 6. 获取所有工作流
GET {{baseUrl}}/workflows
Content-Type: {{contentType}}

### 7. 获取特定工作流详情
GET {{baseUrl}}/workflows/{{createWorkflowFromDsl.response.body.id}}
Content-Type: {{contentType}}

### 8. 执行工作流 - 邮件摘要
POST {{baseUrl}}/workflows/{{createWorkflowFromDsl.response.body.id}}/execute
Content-Type: {{contentType}}

{
  "input": {
    "emailContent": "亲爱的客户，我们很高兴地通知您，您的订单已经发货。订单号：12345，预计3-5个工作日内送达。如有任何问题，请联系我们的客服团队。谢谢您的支持！"
  },
  "context": {
    "userId": "user123",
    "sessionId": "session456"
  }
}

### 9. 执行工作流 - 时间查询
POST {{baseUrl}}/workflows/{{createSimpleWorkflow.response.body.id}}/execute
Content-Type: {{contentType}}

{
  "input": {
    "timezone": "Asia/Shanghai"
  }
}

### 10. 删除工作流
DELETE {{baseUrl}}/workflows/{{createWorkflowFromDsl.response.body.id}}
Content-Type: {{contentType}}

### 11. 验证删除 - 应该返回 404
GET {{baseUrl}}/workflows/{{createWorkflowFromDsl.response.body.id}}
Content-Type: {{contentType}}

### ========================================
### 错误处理测试
### ========================================

### 12. 测试无效 DSL
POST {{baseUrl}}/workflows
Content-Type: {{contentType}}

{
  "name": "无效工作流",
  "description": "测试无效 DSL",
  "dsl": {
    "id": "invalidWorkflow",
    "name": "无效工作流"
    // 缺少必需字段
  }
}

### 13. 测试执行不存在的工作流
POST {{baseUrl}}/workflows/non-existent-id/execute
Content-Type: {{contentType}}

{
  "input": {
    "test": "data"
  }
}

### 14. 测试获取不存在的工作流
GET {{baseUrl}}/workflows/non-existent-id
Content-Type: {{contentType}}

### ========================================
### 使用说明：
### ========================================
# 1. 安装 VS Code 的 REST Client 扩展
# 2. 确保 agent-api 服务正在运行 (http://localhost:3001)
# 3. 先运行 seed 数据: cd apps/agent-api && pnpm run db:seed
# 4. 按顺序执行测试，变量会自动传递
# 5. 观察响应结果验证工作流功能是否正常

### 工作流功能说明：
# - POST /workflows/generate-dsl: 通过自然语言生成工作流 DSL
# - POST /workflows: 基于 DSL 创建工作流实例
# - GET /workflows: 获取所有工作流列表
# - GET /workflows/{id}: 获取特定工作流详情
# - POST /workflows/{id}/execute: 执行工作流
# - DELETE /workflows/{id}: 删除工作流

### DSL 结构说明：
# - id: 工作流唯一标识
# - name: 工作流名称
# - description: 工作流描述
# - version: DSL 版本
# - tools: 使用的工具列表
# - agents: 智能体定义（可选）
# - events: 事件定义
# - steps: 步骤处理逻辑
