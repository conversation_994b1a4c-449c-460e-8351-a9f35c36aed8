### Agent API REST Client 测试文件
### 使用 VS Code REST Client 扩展运行这些测试
### 确保先运行 seed 数据: pnpm run db:seed

@baseUrl = http://localhost:3001/api
@contentType = application/json

### 快速测试变量 (手动设置智能体ID)
# 运行 GET /agents 后，从响应中复制智能体ID到这里
@testAgentId = cmcggp7ho000elnek35m1t6x9

### 1. 健康检查
GET {{baseUrl}}/health
Content-Type: {{contentType}}

### 2. 获取所有智能体 (查看 seed 数据)
GET {{baseUrl}}/agents
Content-Type: {{contentType}}

### 2a. 获取所有工具包 (包含工具详情，用于创建智能体时选择)
GET {{baseUrl}}/toolkits
Content-Type: {{contentType}}

### 3. 创建新的智能体 - 时间助手 (带工具包选择)
# @name createTimeAgent
POST {{baseUrl}}/agents
Content-Type: {{contentType}}

{
  "name": "时间查询助手",
  "description": "专门处理时间相关查询的智能助手，能够获取不同时区的时间信息",
  "prompt": "你是一个专业的时间助手，能够帮助用户获取不同时区的时间信息。请用友好和专业的语气回答用户的时间相关问题。当用户询问时间时，你应该使用 getCurrentTime 工具来获取准确的时间信息。",
  "options": {
    "temperature": 0.7,
    "maxTokens": 1000,
    "model": "gpt-4o-mini"
  },
  "toolkits": [
    {
      "toolkitId": "common-toolkit-01",
      "settings": {}
    }
  ]
}

### 4. 创建新的智能体 - 数据分析师 (带工具包选择)
# @name createDataAgent
POST {{baseUrl}}/agents
Content-Type: {{contentType}}

{
  "name": "数据分析专家",
  "description": "专业的数据分析师，能够分析各种数据并提供有价值的洞察",
  "prompt": "你是一个专业的数据分析师，擅长处理和分析各种类型的数据。你能够：1. 识别数据中的模式和趋势 2. 提供清晰的数据解释 3. 给出基于数据的建议 4. 创建有意义的数据可视化建议。请用专业但易懂的语言解释分析结果。",
  "options": {
    "temperature": 0.3,
    "maxTokens": 1500,
    "model": "gpt-4o-mini"
  },
  "toolkits": [
    {
      "toolkitId": "common-toolkit-01",
      "settings": {}
    },
    {
      "toolkitId": "tool-explorer-toolkit-01",
      "settings": {}
    }
  ]
}

### 5. 创建新的智能体 - 工作流设计师 (不指定工具包，应自动分配 common toolkit)
# @name createWorkflowAgent
POST {{baseUrl}}/agents
Content-Type: {{contentType}}

{
  "name": "AI工作流设计师",
  "description": "专门设计和创建AI工作流的智能助手，能够根据用户需求设计合适的自动化流程",
  "prompt": "你是一个专业的工作流设计专家，能够根据用户需求设计合适的AI工作流。你的职责包括：1. 分析用户的业务需求 2. 识别可以自动化的流程步骤 3. 选择合适的工具和智能体 4. 设计高效的工作流程 5. 提供工作流优化建议。请用清晰的逻辑思维来设计工作流。",
  "options": {
    "temperature": 0.5,
    "maxTokens": 2000,
    "model": "gpt-4o-mini"
  }
}

### 6. 获取特定智能体详情 (自动使用创建的智能体ID)
GET {{baseUrl}}/agents/{{createTimeAgent.response.body.id}}
Content-Type: {{contentType}}

### 6a. 验证时间助手的工具包分配
GET {{baseUrl}}/agents/{{createTimeAgent.response.body.id}}/toolkits
Content-Type: {{contentType}}

### 6b. 验证数据分析师的工具包分配
GET {{baseUrl}}/agents/{{createDataAgent.response.body.id}}/toolkits
Content-Type: {{contentType}}

### 6c. 验证工作流设计师的工具包分配 (应该只有 common toolkit)
GET {{baseUrl}}/agents/{{createWorkflowAgent.response.body.id}}/toolkits
Content-Type: {{contentType}}

### 7. 更新智能体信息 (自动使用创建的智能体ID)
PUT {{baseUrl}}/agents/{{createTimeAgent.response.body.id}}
Content-Type: {{contentType}}

{
  "name": "更新后的时间查询助手",
  "description": "这是更新后的时间助手描述",
  "prompt": "你是一个已经更新的时间助手，具有更强的时间处理能力。",
  "options": {
    "temperature": 0.8,
    "maxTokens": 1200,
    "model": "gpt-4o-mini"
  }
}

### 8. 获取更新后的智能体详情
GET {{baseUrl}}/agents/{{createTimeAgent.response.body.id}}
Content-Type: {{contentType}}

### 9. 软删除智能体 (自动使用创建的智能体ID)
DELETE {{baseUrl}}/agents/{{createTimeAgent.response.body.id}}
Content-Type: {{contentType}}

### 10. 使用数据分析师智能体进行测试
GET {{baseUrl}}/agents/{{createDataAgent.response.body.id}}
Content-Type: {{contentType}}

### 11. 更新数据分析师智能体
PUT {{baseUrl}}/agents/{{createDataAgent.response.body.id}}
Content-Type: {{contentType}}

{
  "name": "高级数据分析专家",
  "description": "升级版的数据分析师，具有更强的分析能力",
  "options": {
    "temperature": 0.2,
    "maxTokens": 2000
  }
}

### 12. 测试错误处理 - 获取不存在的智能体
GET {{baseUrl}}/agents/non-existent-id
Content-Type: {{contentType}}

### 13. 测试错误处理 - 创建无效的智能体 (缺少必需字段)
POST {{baseUrl}}/agents
Content-Type: {{contentType}}

{
  "description": "缺少名称字段的智能体",
  "options": {}
}

### 14. 测试错误处理 - 更新不存在的智能体
PUT {{baseUrl}}/agents/non-existent-id
Content-Type: {{contentType}}

{
  "name": "不存在的智能体"
}

### 15. 测试错误处理 - 删除不存在的智能体
DELETE {{baseUrl}}/agents/non-existent-id
Content-Type: {{contentType}}

### 16. 批量测试 - 创建客服助手
# @name createServiceAgent
POST {{baseUrl}}/agents
Content-Type: {{contentType}}

{
  "name": "客服助手",
  "description": "专业的客户服务智能助手",
  "prompt": "你是一个专业的客服助手，能够友好、耐心地解答客户问题。",
  "options": {
    "temperature": 0.6,
    "maxTokens": 800
  }
}

### 17. 创建代码审查助手
# @name createCodeReviewAgent
POST {{baseUrl}}/agents
Content-Type: {{contentType}}

{
  "name": "代码审查助手",
  "description": "专门进行代码审查和质量检查的智能助手",
  "prompt": "你是一个专业的代码审查专家，能够识别代码中的问题并提供改进建议。",
  "options": {
    "temperature": 0.2,
    "maxTokens": 1500
  }
}

### 18. 创建文档生成助手
# @name createDocAgent
POST {{baseUrl}}/agents
Content-Type: {{contentType}}

{
  "name": "文档生成助手",
  "description": "专门生成技术文档和API文档的智能助手",
  "prompt": "你是一个专业的技术文档编写专家，能够生成清晰、准确的技术文档。",
  "options": {
    "temperature": 0.4,
    "maxTokens": 2000
  }
}

### 19. 使用变量测试 - 批量操作客服助手
GET {{baseUrl}}/agents/{{createServiceAgent.response.body.id}}
Content-Type: {{contentType}}

### 20. 更新客服助手
PUT {{baseUrl}}/agents/{{createServiceAgent.response.body.id}}
Content-Type: {{contentType}}

{
  "name": "高级客服助手",
  "description": "升级版客服助手，具有更强的问题解决能力"
}

### 21. 删除客服助手
DELETE {{baseUrl}}/agents/{{createServiceAgent.response.body.id}}
Content-Type: {{contentType}}

### 22. 最终验证 - 获取所有智能体
GET {{baseUrl}}/agents
Content-Type: {{contentType}}

### 23. 清理测试数据 - 删除创建的智能体
DELETE {{baseUrl}}/agents/{{createDataAgent.response.body.id}}
Content-Type: {{contentType}}

###
DELETE {{baseUrl}}/agents/{{createCodeReviewAgent.response.body.id}}
Content-Type: {{contentType}}

###
DELETE {{baseUrl}}/agents/{{createDocAgent.response.body.id}}
Content-Type: {{contentType}}

### ========================================
### 工具包选择功能测试
### ========================================

### 测试1: 创建智能体时指定多个工具包
# @name createMultiToolkitAgent
POST {{baseUrl}}/agents
Content-Type: {{contentType}}

{
  "name": "多工具包测试智能体",
  "description": "用于测试工具包选择功能的智能体",
  "prompt": "你是一个测试智能体，拥有多个工具包的能力。",
  "options": {
    "temperature": 0.5,
    "maxTokens": 1000,
    "model": "gpt-4o-mini"
  },
  "toolkits": [
    {
      "toolkitId": "common-toolkit-01",
      "settings": {}
    },
    {
      "toolkitId": "tool-explorer-toolkit-01",
      "settings": {}
    }
  ]
}

### 测试2: 验证多工具包智能体的工具包分配
GET {{baseUrl}}/agents/{{createMultiToolkitAgent.response.body.id}}/toolkits
Content-Type: {{contentType}}

### 测试3a: 验证带设置的工具包智能体的工具包分配（应该包含设置）
GET {{baseUrl}}/agents/{{createToolkitWithSettingsAgent.response.body.id}}/toolkits
Content-Type: {{contentType}}

### 测试3: 创建智能体时使用 toolkits 字段（带设置）
# @name createToolkitWithSettingsAgent
POST {{baseUrl}}/agents
Content-Type: {{contentType}}

{
  "name": "带设置的工具包测试智能体",
  "description": "用于测试工具包设置功能的智能体",
  "prompt": "你是一个测试智能体，拥有配置了特定设置的工具包。",
  "options": {
    "temperature": 0.5,
    "maxTokens": 1000,
    "model": "gpt-4o-mini"
  },
  "toolkits": [
    {
      "toolkitId": "common-toolkit-01",
      "settings": {
        "timezone": "Asia/Shanghai",
        "dateFormat": "YYYY-MM-DD HH:mm:ss"
      }
    },
    {
      "toolkitId": "tool-explorer-toolkit-01",
      "settings": {
        "maxResults": 10,
        "includeDescription": true
      }
    }
  ]
}

### 测试4: 创建智能体时不指定工具包（应自动分配 common toolkit）
# @name createAutoToolkitAgent
POST {{baseUrl}}/agents
Content-Type: {{contentType}}

{
  "name": "自动工具包测试智能体",
  "description": "用于测试自动分配工具包功能的智能体",
  "prompt": "你是一个测试智能体，应该自动获得 common toolkit。",
  "options": {
    "temperature": 0.5,
    "maxTokens": 1000,
    "model": "gpt-4o-mini"
  }
}

### 测试4: 验证自动分配的工具包（应该只有 common toolkit）
GET {{baseUrl}}/agents/{{createAutoToolkitAgent.response.body.id}}/toolkits
Content-Type: {{contentType}}

### 测试5: 创建智能体时指定不存在的工具包（应该跳过不存在的工具包）
# @name createInvalidToolkitAgent
POST {{baseUrl}}/agents
Content-Type: {{contentType}}

{
  "name": "无效工具包测试智能体",
  "description": "用于测试无效工具包处理的智能体",
  "prompt": "你是一个测试智能体，用于测试无效工具包的处理。",
  "options": {
    "temperature": 0.5,
    "maxTokens": 1000,
    "model": "gpt-4o-mini"
  },
  "toolkits": [
    {
      "toolkitId": "common-toolkit-01",
      "settings": {}
    },
    {
      "toolkitId": "non-existent-toolkit",
      "settings": {}
    },
    {
      "toolkitId": "tool-explorer-toolkit-01",
      "settings": {}
    }
  ]
}

### 测试6: 验证无效工具包处理（应该只有有效的工具包）
GET {{baseUrl}}/agents/{{createInvalidToolkitAgent.response.body.id}}/toolkits
Content-Type: {{contentType}}

### 测试7: 更新智能体的工具包设置
PUT {{baseUrl}}/agents/{{createToolkitWithSettingsAgent.response.body.id}}
Content-Type: {{contentType}}

{
  "name": "更新后的带设置工具包智能体",
  "toolkits": [
    {
      "toolkitId": "common-toolkit-01",
      "settings": {
        "timezone": "UTC",
        "dateFormat": "ISO8601",
        "locale": "en-US"
      }
    }
  ]
}

### 测试7a: 验证更新后的工具包设置
GET {{baseUrl}}/agents/{{createToolkitWithSettingsAgent.response.body.id}}/toolkits
Content-Type: {{contentType}}

### 清理测试数据
DELETE {{baseUrl}}/agents/{{createMultiToolkitAgent.response.body.id}}
Content-Type: {{contentType}}

###
DELETE {{baseUrl}}/agents/{{createToolkitWithSettingsAgent.response.body.id}}
Content-Type: {{contentType}}

###
DELETE {{baseUrl}}/agents/{{createAutoToolkitAgent.response.body.id}}
Content-Type: {{contentType}}

###
DELETE {{baseUrl}}/agents/{{createInvalidToolkitAgent.response.body.id}}
Content-Type: {{contentType}}

### ========================================
### 智能体对话测试 (使用 seed 数据)
### ========================================

### 24a. 获取所有智能体 (查看可用的智能体ID)
GET {{baseUrl}}/agents
Content-Type: {{contentType}}

### 24b. 快速对话测试 (手动设置智能体ID)
# 步骤：1. 运行上面的 GET /agents 请求
#      2. 从响应中复制一个智能体的ID
#      3. 替换上面 @testAgentId 变量的值
#      4. 运行下面的对话测试
POST {{baseUrl}}/agents/{{testAgentId}}/chat
Content-Type: {{contentType}}

{
  "message": "你好，请介绍一下你自己",
  "context": {
    "sessionId": "quick-test-001"
  }
}

### 24c. 时间查询测试
POST {{baseUrl}}/agents/{{testAgentId}}/chat
Content-Type: {{contentType}}

{
  "message": "现在是几点？",
  "context": {
    "sessionId": "quick-test-002"
  }
}

### ========================================
### 智能体对话测试 (使用动态创建的智能体)
### ========================================

### 24. 与时间助手对话 - 查询当前时间
POST {{baseUrl}}/agents/{{createTimeAgent.response.body.id}}/chat
Content-Type: {{contentType}}

{
  "message": "现在北京时间是几点？",
  "context": {
    "sessionId": "test-session-001"
  }
}

### 25. 与数据分析师对话 - 数据分析请求
POST {{baseUrl}}/agents/{{createDataAgent.response.body.id}}/chat
Content-Type: {{contentType}}

{
  "message": "请帮我分析一下这组销售数据：[100, 150, 120, 180, 200, 160, 220]，并给出趋势分析。",
  "context": {
    "sessionId": "test-session-002",
    "dataType": "sales"
  }
}

### 26. 与客服助手对话 - 客户问题处理
POST {{baseUrl}}/agents/{{createServiceAgent.response.body.id}}/chat
Content-Type: {{contentType}}

{
  "message": "我的订单还没有收到，订单号是12345，请帮我查询一下。",
  "context": {
    "sessionId": "test-session-003",
    "customerType": "vip"
  }
}

### 27. 与代码审查助手对话 - 代码审查
POST {{baseUrl}}/agents/{{createCodeReviewAgent.response.body.id}}/chat
Content-Type: {{contentType}}

{
  "message": "请审查这段代码：\n```javascript\nfunction add(a, b) {\n  return a + b;\n}\n```",
  "context": {
    "sessionId": "test-session-004",
    "language": "javascript"
  }
}

### 28. 与文档生成助手对话 - 生成API文档
POST {{baseUrl}}/agents/{{createDocAgent.response.body.id}}/chat
Content-Type: {{contentType}}

{
  "message": "请为一个用户管理API生成文档，包含创建、查询、更新、删除用户的接口。",
  "context": {
    "sessionId": "test-session-005",
    "docType": "api"
  }
}

### 29. 测试对话错误处理 - 不存在的智能体
POST {{baseUrl}}/agents/non-existent-id/chat
Content-Type: {{contentType}}

{
  "message": "这是一个测试消息",
  "context": {}
}

### 30. 测试对话错误处理 - 空消息
POST {{baseUrl}}/agents/{{createTimeAgent.response.body.id}}/chat
Content-Type: {{contentType}}

{
  "message": "",
  "context": {}
}

### ========================================
### 使用说明：
### ========================================
# 1. 安装 VS Code 的 REST Client 扩展
# 2. 确保 agent-api 服务正在运行 (http://localhost:3001)
# 3. 先运行 seed 数据: cd apps/agent-api && pnpm run db:seed
# 4. 按顺序执行测试，变量会自动传递
# 5. 观察响应结果验证API功能是否正常
# 6. 对话测试需要先创建智能体，然后使用返回的ID进行对话

### 变量说明：
# @baseUrl: API服务的基础URL
# @contentType: 请求内容类型
# {{createTimeAgent.response.body.id}}: 自动获取创建的智能体ID
# {{createDataAgent.response.body.id}}: 自动获取数据分析师ID
# 等等...

### 对话测试说明：
# - 对话接口: POST /agents/{id}/chat
# - 请求体包含 message (必需) 和 context (可选)
# - 响应包含智能体的回复和相关信息
# - 智能体会根据其配置的工具和提示词进行回复

### 工具包选择功能说明：
# - 创建智能体时通过 toolkits 字段选择工具包：
#   toolkits: [{"toolkitId": "toolkit-id", "settings": {...}}]
# - 如果不指定 toolkits 字段，系统会自动分配 common-toolkit-01
# - common-toolkit-01 总是会被包含，即使没有明确指定
# - 工具包设置 (settings) 可以为空对象 {} 或包含特定配置
# - 更新智能体时，如果提供了工具包配置，会完全替换现有的工具包分配
# - 可以通过 GET /toolkits 获取所有可用工具包（包含工具详情）
# - 可以通过 GET /agents/{id}/toolkits 查看智能体的工具包分配（包含设置）
# - 不存在的工具包会被自动跳过，不会影响智能体创建
