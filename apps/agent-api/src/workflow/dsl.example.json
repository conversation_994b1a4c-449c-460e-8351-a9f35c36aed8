{"id": "workflowMailSummarySend", "name": "AI邮件摘要与推送", "description": "AI自动摘要邮件并通过企业微信推送", "tools": ["summarize", "sendWeCom"], "agents": [{"name": "mailSummarizerAgent", "description": "基于LLM对邮件内容进行结构化摘要。", "prompt": "请总结输入的邮件内容，简洁扼要，输出JSON：{\"summary\": \"...\"}", "output": {"summary": "string"}, "tools": ["summarize"]}], "content": {}, "events": [{"type": "WORKFLOW_START", "data": {"emailContent": "string"}}, {"type": "SUMMARY_DONE", "data": {"summary": "string"}}, {"type": "WORKFLOW_STOP", "data": {"sendResult": "string"}}], "steps": [{"event": "WORKFLOW_START", "handle": "async (event, context) => { const response = await mailSummarizerAgent.run({ message: event.data.emailContent }); const resultString = response.data.result; const parsedResult = JSON.parse(resultString); return { type: 'SUMMARY_DONE', data: { summary: parsedResult.summary } }; }"}, {"event": "SUMMARY_DONE", "handle": "async (event, context) => { const sendResult = await sendWeCom.call({ text: event.data.summary }); return { type: 'WORKFLOW_STOP', data: { sendResult } }; }"}]}