{"name": "agent-api", "version": "0.0.1", "private": true, "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "typecheck": "tsc --noEmit", "db:generate": "prisma generate --schema=./prisma/schema.prisma", "db:migrate": "prisma migrate dev --schema=./prisma/schema.prisma", "db:deploy": "prisma migrate deploy --schema=./prisma/schema.prisma", "db:push": "prisma db push --schema=./prisma/schema.prisma", "db:studio": "prisma studio --schema=./prisma/schema.prisma", "db:seed": "ts-node prisma/seed.ts", "db:reset": "prisma migrate reset --schema=./prisma/schema.prisma --force && pnpm run db:seed"}, "dependencies": {"@llamaindex/openai": "^0.4.3", "@llamaindex/workflow": "^1.1.7", "@nestjs/common": "^10.4.8", "@nestjs/config": "^4.0.2", "@nestjs/core": "^10.4.8", "@nestjs/platform-express": "^10.4.8", "@prisma/client": "5.10.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "dayjs": "^1.11.13", "https-proxy-agent": "^7.0.6", "llamaindex": "^0.11.6", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "zod": "^3.23.8"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/node": "^20.17.19", "@types/supertest": "^6.0.0", "@workspace/eslint-config": "workspace:*", "@workspace/typescript-config": "workspace:*", "jest": "^29.5.0", "prettier": "^3.0.0", "prisma": "5.10.2", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}